"""
Service d'authentification.
"""
from typing import Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.models.user import User
from app.schemas.auth import UserCreate, UserLogin
from app.core.security import create_access_token


class AuthService:
    """Service pour la gestion de l'authentification."""

    @staticmethod
    def authenticate_user(db: Session, username: str) -> Optional[User]:
        """Authentifie un utilisateur par nom d'utilisateur uniquement."""
        user = db.query(User).filter(User.username == username).first()
        if not user:
            return None
        return user

    @staticmethod
    def create_user(db: Session, user_data: UserCreate) -> User:
        """Crée un nouvel utilisateur."""
        # Vérifier si l'utilisateur existe déjà
        existing_user = db.query(User).filter(
            (User.username == user_data.username) | (User.email == user_data.email)
        ).first()
        
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username or email already registered"
            )
        
        # Créer le nouvel utilisateur
        db_user = User(
            username=user_data.username,
            email=user_data.email,
            full_name=user_data.full_name,
            nationalite=user_data.nationalite,
            hashed_password=""  # Pas de mot de passe requis
        )
        
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user

    @staticmethod
    def get_user_by_username(db: Session, username: str) -> Optional[User]:
        """Récupère un utilisateur par son nom d'utilisateur."""
        return db.query(User).filter(User.username == username).first()

    @staticmethod
    def login_user(db: Session, login_data: UserLogin) -> dict:
        """Connecte un utilisateur et retourne un token."""
        user = AuthService.authenticate_user(db, login_data.username)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Nom d'utilisateur incorrect",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )
        
        # Créer le token d'accès
        access_token = create_access_token(data={"sub": user.username})
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "full_name": user.full_name
            }
        }
