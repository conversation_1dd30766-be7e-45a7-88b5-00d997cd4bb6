../../../bin/black,sha256=DkrK8M0xuMyaYOmcO12215uGqSxYs7eXDXD4YRDX9iE,269
../../../bin/blackd,sha256=1mtsV7bBs7aJUMo5uIS8mJ382hHoM37jcZMGd77elUE,270
__pycache__/_black_version.cpython-312.pyc,,
_black_version.py,sha256=aVU1-lQYwAKQaU3bqDukH3x3JosWRWE4q8PQgFydO6k,20
black-23.11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
black-23.11.0.dist-info/METADATA,sha256=E6Jo0a2mXlCG_dCtb9fmKwvrIk0IPhRla0vQ3opq5ek,66924
black-23.11.0.dist-info/RECORD,,
black-23.11.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black-23.11.0.dist-info/WHEEL,sha256=9QBuHhg6FNW7lppboF2vKVbCGTVzsFykgRQjjlajrhA,87
black-23.11.0.dist-info/entry_points.txt,sha256=qBIyywHwGRkJj7kieq86kqf77rz3qGC4Joj36lHnxwc,78
black-23.11.0.dist-info/licenses/AUTHORS.md,sha256=8drxTtCp41j9z9NFJ9U37R1m9qL0zwTMELvgHFFkwao,8092
black-23.11.0.dist-info/licenses/LICENSE,sha256=nAQo8MO0d5hQz1vZbhGqqK_HLUqG1KNiI9erouWNbgA,1080
black/__init__.py,sha256=pZ-p5gbGibyCPKttKB5ipzwJiVDFk2esTC6PgfpDsm4,48161
black/__main__.py,sha256=mogeA4o9zt4w-ufKvaQjSEhtSgQkcMVLK9ChvdB5wH8,47
black/__pycache__/__init__.cpython-312.pyc,,
black/__pycache__/__main__.cpython-312.pyc,,
black/__pycache__/_width_table.cpython-312.pyc,,
black/__pycache__/brackets.cpython-312.pyc,,
black/__pycache__/cache.cpython-312.pyc,,
black/__pycache__/comments.cpython-312.pyc,,
black/__pycache__/concurrency.cpython-312.pyc,,
black/__pycache__/const.cpython-312.pyc,,
black/__pycache__/debug.cpython-312.pyc,,
black/__pycache__/files.cpython-312.pyc,,
black/__pycache__/handle_ipynb_magics.cpython-312.pyc,,
black/__pycache__/linegen.cpython-312.pyc,,
black/__pycache__/lines.cpython-312.pyc,,
black/__pycache__/mode.cpython-312.pyc,,
black/__pycache__/nodes.cpython-312.pyc,,
black/__pycache__/numerics.cpython-312.pyc,,
black/__pycache__/output.cpython-312.pyc,,
black/__pycache__/parsing.cpython-312.pyc,,
black/__pycache__/ranges.cpython-312.pyc,,
black/__pycache__/report.cpython-312.pyc,,
black/__pycache__/rusty.cpython-312.pyc,,
black/__pycache__/strings.cpython-312.pyc,,
black/__pycache__/trans.cpython-312.pyc,,
black/_width_table.py,sha256=2lSnE4s_nVXXfIj9hP2qWASqX8I003WxBM5xPnelDrQ,10761
black/brackets.py,sha256=pIavHXe4tm7mLWMhfqXRiLI52-8XI9b3szyBF3kR0Mk,12474
black/cache.py,sha256=47z4njpWanyyUONeFS-vvBs-qVIKPHSV5AK0OjkSEA0,4579
black/comments.py,sha256=fZCN_5YaYYDnDWtYeQ-4ivzYaCdV9vZkarizASpRezM,14111
black/concurrency.py,sha256=jQTysDbMlvOexIjWpYHD296SBpNyd6ndVJYPKlBZkIY,6408
black/const.py,sha256=U7cDnhWljmrieOtPBUdO2Vcz69J_VXB6-Br94wuCVuo,321
black/debug.py,sha256=HmpJna5KhwLpgQkqEGR2FX6GRmJ2tFh4Jl6X2aMR3ak,1906
black/files.py,sha256=m_FTnA6cTrQGIGz8YGrWctxTacy4GdmX4ySvma3MkCs,13853
black/handle_ipynb_magics.py,sha256=pw_WWBMxY9MsFdjzDI2knW1r8WsBQvwuPndf_awh1mM,13466
black/linegen.py,sha256=Jrb08cTO4bG4RFEHVfANyJ0YFC3WbCS5miGYPTOLPRQ,65788
black/lines.py,sha256=nGPFOfTpeSFbB0B9MQzTQy7prD5dFNUlsCQeGR9lClk,40444
black/mode.py,sha256=xH68mq878JRD7BSDHcPVkSaZM4iEe2XhI5efEQ1iQhc,8359
black/nodes.py,sha256=TZFwPfRkvV3GF7pPYvkiU2sjWz5K6LhX42qWk96DR7w,27718
black/numerics.py,sha256=nUUfLZH0pfZ6TpGYS76EEu4tb3gMLYaU2OrQDvZCvks,1654
black/output.py,sha256=qfdOuT8z5WSm_GxwP24X5XrjrxPacjj1k1gC40crJGw,3486
black/parsing.py,sha256=HZEP7SEOTvgq0kkJBbL791V_n5CiFDAhUht-ICoc_mw,7332
black/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black/ranges.py,sha256=9BqSexDL9HSTJUuyScWKGStZjOrqqk4lhYwGsozhpAM,18882
black/report.py,sha256=igkNi8iR5FqSa1sXddS-HnoqW7Cq7PveCmFCfd-pN6w,3452
black/rusty.py,sha256=4LKo3KTUWYZ2cN6QKmwwZVbsCNt2fpu5lzue2V-uxIA,557
black/strings.py,sha256=Ra4sHKZWmNGLA7QIYH4e6EApGn40Bt-7ipbwsbMoGz8,11098
black/trans.py,sha256=Ul5pjpjEGEgoAzrmartJIaSSSJhzfxNxNQOGUQqPySs,92609
blackd/__init__.py,sha256=bhi6krqe72SmRzT0m4qIDgiXLZQNyyy_kUtuX-JmOH0,8298
blackd/__main__.py,sha256=L4xAcDh1K5zb6SsJB102AewW2G13P9-w2RiEwuFj8WA,37
blackd/__pycache__/__init__.cpython-312.pyc,,
blackd/__pycache__/__main__.cpython-312.pyc,,
blackd/__pycache__/middlewares.cpython-312.pyc,,
blackd/middlewares.py,sha256=QS7cs86Ojuaqh64dGneimhJ-f30rDI646c27ts4Dwh0,1585
blib2to3/Grammar.txt,sha256=qUL_B_u7lqX-83Bas6u8Ckw8q4ea-cQS3Jlv3_i0hrI,11351
blib2to3/LICENSE,sha256=V4mIG4rrnJH1g19bt8q-hKD-zUuyvi9UyeaVenjseZ0,12762
blib2to3/PatternGrammar.txt,sha256=7lul2ztnIqDi--JWDrwciD5yMo75w7TaHHxdHMZJvOM,793
blib2to3/README,sha256=QYZYIfb1NXTTYqDV4kn8oRcNG_qlTFYH1sr3V1h65ko,1074
blib2to3/__init__.py,sha256=9_8wL9Scv8_Cs8HJyJHGvx1vwXErsuvlsAqNZLcJQR0,8
blib2to3/__pycache__/__init__.cpython-312.pyc,,
blib2to3/__pycache__/pygram.cpython-312.pyc,,
blib2to3/__pycache__/pytree.cpython-312.pyc,,
blib2to3/pgen2/__init__.py,sha256=hY6w9QUzvTvRb-MoFfd_q_7ZLt6IUHC2yxWCfsZupQA,143
blib2to3/pgen2/__pycache__/__init__.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/conv.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/driver.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/grammar.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/literals.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/parse.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/pgen.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/token.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/tokenize.cpython-312.pyc,,
blib2to3/pgen2/conv.py,sha256=vH8a_gkalWRNxuNPRxkoigw8_UobdHHSw-PyUcUuH8I,9587
blib2to3/pgen2/driver.py,sha256=ppu5nhL9iL2eUuWR0pzOxV3FFqM04F3rt-Ckwf5tz_I,10565
blib2to3/pgen2/grammar.py,sha256=WQBX_vZFq8RNVNPX49J8oNdwXeWhXhizUu5vPwD0ZVM,6858
blib2to3/pgen2/literals.py,sha256=_LyRryELzqarFkW3OAEZzZ-yppCTm9g0mjqqQ2XygKE,1614
blib2to3/pgen2/parse.py,sha256=ufCEykU-ujdLEei9o1z5fl5ohkGpid4FVRegwe0WhMA,15657
blib2to3/pgen2/pgen.py,sha256=iQH8W999TKUT5AhuOpW38ZynwSACkVNV-I6z8kyQozY,15428
blib2to3/pgen2/token.py,sha256=iT30kH8_qqhvxuzyUpiIiO3SGxuxqopZBBg-s1x8Vzo,1805
blib2to3/pgen2/tokenize.py,sha256=YoQZYgETSjn2WCc3N0fL67vq2oakg85K4MLfQ_NhFcI,23111
blib2to3/pygram.py,sha256=7C4ciX12W3SKWNI9WkTQRhoXMJkROSk1GMS18snC51o,4810
blib2to3/pytree.py,sha256=dedSbfx56FTkyTOA1A-I4eTVyDuZ0VRZ_eq0H5HmgLc,32569
