"""
Script de démarrage de l'application.
"""
import uvicorn
import os
import sys

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Démarre l'application FastAPI."""
    print("=== Démarrage de l'application Gestion des Visiteurs ===")
    print("Application disponible sur: http://localhost:8000")
    print("Documentation API: http://localhost:8000/docs")
    print("Utilisateur par défaut: admin")
    print("Appuyez sur Ctrl+C pour arrêter l'application")
    print("=" * 60)
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

if __name__ == "__main__":
    main()
