"""
Schémas Pydantic pour l'authentification.
"""
from typing import Optional
from pydantic import BaseModel, EmailStr


class UserLogin(BaseModel):
    """Schéma pour la connexion utilisateur."""
    username: str
    remember_me: bool = False


class UserCreate(BaseModel):
    """Schéma pour la création d'utilisateur."""
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    nationalite: str


class UserResponse(BaseModel):
    """Schéma pour la réponse utilisateur."""
    id: int
    username: str
    email: str
    full_name: Optional[str] = None
    is_active: bool = True

    class Config:
        from_attributes = True


class Token(BaseModel):
    """Schéma pour le token JWT."""
    access_token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    """Schéma pour les données du token."""
    username: Optional[str] = None
