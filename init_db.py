"""
Script d'initialisation de la base de données.
"""
import os
import sys

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.database import engine, Base
from app.models.user import User
from app.models.visiteur import Visiteur
from app.services.auth_service import AuthService
from app.schemas.auth import UserCreate
from sqlalchemy.orm import sessionmaker

def create_tables():
    """Crée toutes les tables de la base de données."""
    print("Création des tables...")
    Base.metadata.create_all(bind=engine)
    print("Tables créées avec succès!")

def create_default_user():
    """Crée un utilisateur par défaut."""
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Vérifier si un utilisateur existe déjà
        existing_user = db.query(User).first()
        if existing_user:
            print("Un utilisateur existe déjà.")
            return
        
        # Créer un utilisateur par défaut
        user_data = UserCreate(
            username="admin",
            email="<EMAIL>",
            full_name="Administrateur",
            nationalite="Congolaise"
        )
        
        user = AuthService.create_user(db, user_data)
        print(f"Utilisateur par défaut créé: {user.username}")
        
    except Exception as e:
        print(f"Erreur lors de la création de l'utilisateur: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """Fonction principale d'initialisation."""
    print("=== Initialisation de la base de données ===")
    
    # Créer le dossier de la base de données s'il n'existe pas
    db_dir = os.path.dirname("visiteurs.db")
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir)
    
    # Créer les tables
    create_tables()
    
    # Créer l'utilisateur par défaut
    create_default_user()
    
    # Créer le dossier d'upload s'il n'existe pas
    upload_dir = "static/uploads/photos"
    if not os.path.exists(upload_dir):
        os.makedirs(upload_dir)
        print(f"Dossier d'upload créé: {upload_dir}")
    
    print("=== Initialisation terminée ===")
    print("\nPour démarrer l'application:")
    print("python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    print("\nAccès à l'application: http://localhost:8000")
    print("Utilisateur par défaut: admin")

if __name__ == "__main__":
    main()
