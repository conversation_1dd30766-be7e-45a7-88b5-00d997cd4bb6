{% extends "base.html" %}

{% block title %}Formulaire Visiteur - Gestion des Visiteurs{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="h-12 w-12 bg-primary-500 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">FICHE DE RÉCEPTION</h1>
                        <p class="text-gray-600">Enregistrement d'un nouveau visiteur</p>
                    </div>
                </div>
                <button id="logoutButton" class="text-gray-500 hover:text-gray-700">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Formulaire principal -->
        <div class="bg-white rounded-lg shadow-sm p-8">
            <!-- Section Photo -->
            <div class="mb-8 flex justify-between items-start">
                <div class="flex-1">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Photo du visiteur</h2>
                    <div class="flex items-center space-x-4">
                        <button type="button" id="capturePhotoBtn" class="btn-primary">
                            <svg class="h-5 w-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Prendre une photo
                        </button>
                        <input type="file" id="photoUpload" accept="image/*" class="hidden">
                        <button type="button" id="uploadPhotoBtn" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            <svg class="h-5 w-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            Choisir un fichier
                        </button>
                    </div>
                </div>
                <!-- Aperçu de la photo -->
                <div class="ml-8">
                    <div id="photoPreview" class="hidden">
                        <img id="previewImage" src="" alt="Photo du visiteur" class="w-32 h-40 object-cover rounded-lg border-2 border-gray-300 shadow-sm">
                        <button type="button" id="removePhotoBtn" class="mt-2 text-red-600 hover:text-red-800 text-sm">
                            Supprimer la photo
                        </button>
                    </div>
                    <div id="photoPlaceholder" class="w-32 h-40 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                        <div class="text-center text-gray-500">
                            <svg class="h-8 w-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <p class="text-xs">Photo</p>
                        </div>
                    </div>
                </div>
            </div>

            <form id="visiteurForm" class="space-y-8">
                <!-- Messages -->
                <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <span id="errorText"></span>
                </div>
                <div id="successMessage" class="hidden bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <span id="successText"></span>
                </div>

                <!-- Section 1: Informations personnelles -->
                <div class="border-b border-gray-200 pb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">I. INFORMATIONS PERSONNELLES</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <label for="nom" class="block text-sm font-medium text-gray-700 mb-2">
                                Nom <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="nom"
                                name="nom"
                                required
                                class="input-field"
                                placeholder="Nom de famille"
                            >
                        </div>
                        <div>
                            <label for="prenom" class="block text-sm font-medium text-gray-700 mb-2">
                                Prénom <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="prenom"
                                name="prenom"
                                required
                                class="input-field"
                                placeholder="Prénom"
                            >
                        </div>
                        <div>
                            <label for="postnom" class="block text-sm font-medium text-gray-700 mb-2">
                                Post-nom <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="postnom"
                                name="postnom"
                                required
                                class="input-field"
                                placeholder="Post-nom"
                            >
                        </div>
                        <div>
                            <label for="sexe" class="block text-sm font-medium text-gray-700 mb-2">
                                Sexe <span class="text-red-500">*</span>
                            </label>
                            <select
                                id="sexe"
                                name="sexe"
                                required
                                class="input-field"
                            >
                                <option value="">Sélectionner</option>
                                <option value="M">Masculin</option>
                                <option value="F">Féminin</option>
                            </select>
                        </div>
                        <div>
                            <label for="nationalite" class="block text-sm font-medium text-gray-700 mb-2">
                                Nationalité <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="nationalite"
                                name="nationalite"
                                required
                                class="input-field"
                                placeholder="Nationalité"
                            >
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                Email
                            </label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                class="input-field"
                                placeholder="<EMAIL>"
                            >
                        </div>
                    </div>

                    <!-- Téléphones -->
                    <div class="mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Numéros de téléphone</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="telephone1" class="block text-sm font-medium text-gray-700 mb-2">
                                    Téléphone 1 <span class="text-red-500">*</span>
                                </label>
                                <input
                                    type="tel"
                                    id="telephone1"
                                    name="telephone1"
                                    required
                                    class="input-field"
                                    placeholder="+243 XX XXX XXXX"
                                >
                            </div>
                            <div>
                                <label for="telephone2" class="block text-sm font-medium text-gray-700 mb-2">
                                    Téléphone 2 <span class="text-red-500">*</span>
                                </label>
                                <input
                                    type="tel"
                                    id="telephone2"
                                    name="telephone2"
                                    required
                                    class="input-field"
                                    placeholder="+243 XX XXX XXXX"
                                >
                            </div>
                            <div>
                                <label for="telephone3" class="block text-sm font-medium text-gray-700 mb-2">
                                    Téléphone 3
                                </label>
                                <input
                                    type="tel"
                                    id="telephone3"
                                    name="telephone3"
                                    class="input-field"
                                    placeholder="+243 XX XXX XXXX"
                                >
                            </div>
                            <div>
                                <label for="telephone4" class="block text-sm font-medium text-gray-700 mb-2">
                                    Téléphone 4
                                </label>
                                <input
                                    type="tel"
                                    id="telephone4"
                                    name="telephone4"
                                    class="input-field"
                                    placeholder="+243 XX XXX XXXX"
                                >
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 2: Informations professionnelles -->
                <div class="border-b border-gray-200 pb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">II. ORGANISATION</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="organisation" class="block text-sm font-medium text-gray-700 mb-2">
                                Organisation/Entreprise
                            </label>
                            <input 
                                type="text" 
                                id="organisation" 
                                name="organisation" 
                                class="input-field"
                                placeholder="Nom de l'organisation"
                            >
                        </div>
                        <div>
                            <label for="fonction" class="block text-sm font-medium text-gray-700 mb-2">
                                Fonction <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="fonction"
                                name="fonction"
                                required
                                class="input-field"
                                placeholder="Poste occupé"
                            >
                        </div>
                    </div>
                </div>

                <!-- Section 3: Informations de visite -->
                <div class="border-b border-gray-200 pb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">III. DÉTAILS DE LA VISITE</h2>
                    <div class="space-y-6">
                        <div>
                            <label for="objet_visite" class="block text-sm font-medium text-gray-700 mb-2">
                                Objet de la visite <span class="text-red-500">*</span>
                            </label>
                            <textarea
                                id="objet_visite"
                                name="objet_visite"
                                required
                                rows="3"
                                class="input-field"
                                placeholder="Décrivez l'objet de votre visite..."
                            ></textarea>
                        </div>
                        <div>
                            <label for="personne_visitee" class="block text-sm font-medium text-gray-700 mb-2">
                                Personne visitée <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="personne_visitee"
                                name="personne_visitee"
                                required
                                class="input-field"
                                placeholder="Nom de la personne à rencontrer"
                            >
                        </div>
                        <div>
                            <label for="badge_numero" class="block text-sm font-medium text-gray-700 mb-2">
                                Numéro de badge
                            </label>
                            <input
                                type="text"
                                id="badge_numero"
                                name="badge_numero"
                                class="input-field"
                                placeholder="Numéro du badge visiteur"
                            >
                        </div>
                    </div>
                </div>

                <!-- Section 4: Engagement -->
                <div class="border-b border-gray-200 pb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">IV. ENGAGEMENT</h2>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <p class="text-sm text-gray-700 leading-relaxed mb-4">
                            Je reconnais avoir pris connaissance des consignes de sécurité et m'engage à les respecter pendant toute la durée de ma visite. Je m'engage également à porter le badge visiteur de manière visible et à le restituer en fin de visite.
                        </p>
                        <div class="flex items-center">
                            <input
                                id="engagement"
                                name="engagement"
                                type="checkbox"
                                required
                                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            >
                            <label for="engagement" class="ml-2 block text-sm text-gray-700">
                                J'accepte les conditions et m'engage à respecter les consignes <span class="text-red-500">*</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="flex justify-between items-center pt-6">
                    <button
                        type="button"
                        id="resetButton"
                        class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                    >
                        Réinitialiser
                    </button>
                    <button
                        type="submit"
                        id="submitButton"
                        class="btn-primary px-8 py-3 text-lg font-semibold"
                    >
                        <span id="submitButtonText">Enregistrer le visiteur</span>
                        <svg id="submitSpinner" class="hidden animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>

        <!-- Lien vers la liste des visiteurs -->
        <div class="mt-6 text-center">
            <a href="/visiteurs" class="text-primary-600 hover:text-primary-500 font-medium">
                Voir la liste des visiteurs
            </a>
        </div>
    </div>
</div>
{% endblock %}

<!-- Modal pour la caméra -->
<div id="cameraModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Prendre une photo</h3>
                <button id="closeCameraModal" class="text-gray-400 hover:text-gray-600">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="text-center">
                <video id="cameraVideo" width="400" height="300" autoplay class="mx-auto rounded-lg border"></video>
                <canvas id="cameraCanvas" width="400" height="300" class="hidden"></canvas>
                <div class="mt-4 space-x-4">
                    <button id="captureBtn" class="btn-primary">
                        <svg class="h-5 w-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Capturer
                    </button>
                    <button id="retakeBtn" class="hidden px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        Reprendre
                    </button>
                    <button id="confirmCaptureBtn" class="hidden btn-primary">
                        Confirmer
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_scripts %}
<script>
    // Vérifier l'authentification
    const token = localStorage.getItem('access_token');
    if (!token) {
        window.location.href = '/login';
    }

    // Variables globales pour la caméra
    let currentStream = null;
    let capturedPhotoBlob = null;
    let currentPhotoPath = null;

    // Fonctions pour la gestion de la caméra
    async function startCamera() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: 400,
                    height: 300,
                    facingMode: 'user'
                }
            });
            currentStream = stream;
            document.getElementById('cameraVideo').srcObject = stream;
            document.getElementById('cameraModal').classList.remove('hidden');
        } catch (error) {
            alert('Erreur d\'accès à la caméra: ' + error.message);
        }
    }

    function stopCamera() {
        if (currentStream) {
            currentStream.getTracks().forEach(track => track.stop());
            currentStream = null;
        }
    }

    function capturePhoto() {
        const video = document.getElementById('cameraVideo');
        const canvas = document.getElementById('cameraCanvas');
        const context = canvas.getContext('2d');

        // Dessiner l'image de la vidéo sur le canvas
        context.drawImage(video, 0, 0, 400, 300);

        // Convertir en blob
        canvas.toBlob(function(blob) {
            capturedPhotoBlob = blob;

            // Afficher l'aperçu
            const url = URL.createObjectURL(blob);
            video.style.display = 'none';
            canvas.style.display = 'block';
            canvas.style.margin = '0 auto';

            // Afficher les boutons de confirmation
            document.getElementById('captureBtn').classList.add('hidden');
            document.getElementById('retakeBtn').classList.remove('hidden');
            document.getElementById('confirmCaptureBtn').classList.remove('hidden');
        }, 'image/jpeg', 0.8);
    }

    function retakePhoto() {
        const video = document.getElementById('cameraVideo');
        const canvas = document.getElementById('cameraCanvas');

        video.style.display = 'block';
        canvas.style.display = 'none';

        document.getElementById('captureBtn').classList.remove('hidden');
        document.getElementById('retakeBtn').classList.add('hidden');
        document.getElementById('confirmCaptureBtn').classList.add('hidden');

        capturedPhotoBlob = null;
    }

    async function confirmCapture() {
        if (!capturedPhotoBlob) return;

        try {
            // Upload de la photo
            const formData = new FormData();
            formData.append('file', capturedPhotoBlob, 'photo.jpg');

            const response = await fetch('/api/v1/upload/photo', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                currentPhotoPath = result.data.photo_path;
                displayPhoto(currentPhotoPath);
                closeCameraModal();
            } else {
                alert('Erreur lors de l\'upload: ' + result.message);
            }
        } catch (error) {
            alert('Erreur lors de l\'upload: ' + error.message);
        }
    }

    function displayPhoto(photoPath) {
        const preview = document.getElementById('photoPreview');
        const placeholder = document.getElementById('photoPlaceholder');
        const previewImage = document.getElementById('previewImage');

        previewImage.src = photoPath;
        preview.classList.remove('hidden');
        placeholder.classList.add('hidden');
    }

    function removePhoto() {
        const preview = document.getElementById('photoPreview');
        const placeholder = document.getElementById('photoPlaceholder');

        preview.classList.add('hidden');
        placeholder.classList.remove('hidden');
        currentPhotoPath = null;
    }

    function closeCameraModal() {
        document.getElementById('cameraModal').classList.add('hidden');
        stopCamera();
        retakePhoto(); // Reset camera state
    }

    // Event listeners pour la photo
    document.getElementById('capturePhotoBtn').addEventListener('click', startCamera);
    document.getElementById('uploadPhotoBtn').addEventListener('click', () => {
        document.getElementById('photoUpload').click();
    });
    document.getElementById('removePhotoBtn').addEventListener('click', removePhoto);
    document.getElementById('closeCameraModal').addEventListener('click', closeCameraModal);
    document.getElementById('captureBtn').addEventListener('click', capturePhoto);
    document.getElementById('retakeBtn').addEventListener('click', retakePhoto);
    document.getElementById('confirmCaptureBtn').addEventListener('click', confirmCapture);

    // Upload de fichier photo
    document.getElementById('photoUpload').addEventListener('change', async function(e) {
        const file = e.target.files[0];
        if (!file) return;

        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/v1/upload/photo', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                currentPhotoPath = result.data.photo_path;
                displayPhoto(currentPhotoPath);
            } else {
                alert('Erreur lors de l\'upload: ' + result.message);
            }
        } catch (error) {
            alert('Erreur lors de l\'upload: ' + error.message);
        }
    });

    // Gestion du formulaire visiteur
    document.getElementById('visiteurForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const submitButton = document.getElementById('submitButton');
        const submitButtonText = document.getElementById('submitButtonText');
        const submitSpinner = document.getElementById('submitSpinner');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        // Désactiver le bouton et afficher le spinner
        submitButton.disabled = true;
        submitButtonText.textContent = 'Enregistrement...';
        submitSpinner.classList.remove('hidden');

        // Masquer les messages précédents
        errorMessage.classList.add('hidden');
        successMessage.classList.add('hidden');

        try {
            const formData = new FormData(this);
            const data = {
                nom: formData.get('nom'),
                prenom: formData.get('prenom'),
                postnom: formData.get('postnom'),
                sexe: formData.get('sexe'),
                nationalite: formData.get('nationalite'),
                telephone1: formData.get('telephone1'),
                telephone2: formData.get('telephone2'),
                telephone3: formData.get('telephone3') || null,
                telephone4: formData.get('telephone4') || null,
                email: formData.get('email') || null,
                organisation: formData.get('organisation') || null,
                fonction: formData.get('fonction'),
                objet_visite: formData.get('objet_visite'),
                personne_visitee: formData.get('personne_visitee'),
                badge_numero: formData.get('badge_numero') || null,
                photo_path: currentPhotoPath
            };

            const response = await fetch('/api/v1/visiteurs/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                // Afficher le message de succès
                document.getElementById('successText').textContent = result.message;
                successMessage.classList.remove('hidden');

                // Réinitialiser le formulaire
                this.reset();

                // Faire défiler vers le haut
                window.scrollTo(0, 0);
            } else {
                // Afficher l'erreur
                document.getElementById('errorText').textContent = result.message;
                errorMessage.classList.remove('hidden');
                window.scrollTo(0, 0);
            }
        } catch (error) {
            // Afficher l'erreur
            document.getElementById('errorText').textContent = 'Erreur lors de l\'enregistrement. Veuillez réessayer.';
            errorMessage.classList.remove('hidden');
            window.scrollTo(0, 0);
        } finally {
            // Réactiver le bouton
            submitButton.disabled = false;
            submitButtonText.textContent = 'Enregistrer le visiteur';
            submitSpinner.classList.add('hidden');
        }
    });

    // Bouton de réinitialisation
    document.getElementById('resetButton').addEventListener('click', function() {
        if (confirm('Êtes-vous sûr de vouloir réinitialiser le formulaire ?')) {
            document.getElementById('visiteurForm').reset();
            document.getElementById('errorMessage').classList.add('hidden');
            document.getElementById('successMessage').classList.add('hidden');
        }
    });

    // Bouton de déconnexion
    document.getElementById('logoutButton').addEventListener('click', function() {
        if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
            localStorage.removeItem('access_token');
            localStorage.removeItem('user');
            window.location.href = '/login';
        }
    });

    // Validation des téléphones en temps réel
    ['telephone1', 'telephone2', 'telephone3', 'telephone4'].forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, ''); // Supprimer tout sauf les chiffres

                // Formater le numéro (format congolais)
                if (value.length >= 10) {
                    if (value.startsWith('243')) {
                        // Format international: +243 XX XXX XXXX
                        value = value.replace(/(\d{3})(\d{2})(\d{3})(\d{4})/, '+$1 $2 $3 $4');
                    } else if (value.length === 10) {
                        // Format local: 0XX XXX XXXX
                        value = value.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3');
                    }
                }

                e.target.value = value;
            });
        }
    });
</script>
{% endblock %}
