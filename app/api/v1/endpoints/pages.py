"""
Endpoints pour servir les pages HTML.
"""
from fastapi import APIRouter, Request, Depends
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse

templates = Jinja2Templates(directory="templates")
router = APIRouter()


@router.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """Page de connexion."""
    return templates.TemplateResponse("auth/login.html", {"request": request})


@router.get("/dashboard", response_class=HTMLResponse)
async def dashboard_page(request: Request):
    """Page du tableau de bord."""
    return templates.TemplateResponse("visiteurs/form.html", {"request": request})


@router.get("/visiteurs", response_class=HTMLResponse)
async def visiteurs_page(request: Request):
    """Page de liste des visiteurs."""
    return templates.TemplateResponse("visiteurs/list.html", {"request": request})


@router.get("/", response_class=HTMLResponse)
async def home_page(request: Request):
    """Page d'accueil - redirige vers login."""
    return templates.TemplateResponse("auth/login.html", {"request": request})
