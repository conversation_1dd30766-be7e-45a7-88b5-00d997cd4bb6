"""
Schémas Pydantic pour les visiteurs.
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, validator


class VisiteurBase(BaseModel):
    """Schéma de base pour un visiteur."""
    nom: str
    prenom: str
    telephone: str
    email: Optional[EmailStr] = None
    organisation: Optional[str] = None
    fonction: Optional[str] = None
    objet_visite: str
    personne_visitee: str
    badge_numero: Optional[str] = None
    
    @validator('telephone')
    def validate_telephone(cls, v):
        """Valide le format du numéro de téléphone."""
        # Supprime les espaces et caractères spéciaux
        cleaned = ''.join(filter(str.isdigit, v))
        if len(cleaned) < 8:
            raise ValueError('Le numéro de téléphone doit contenir au moins 8 chiffres')
        return v


class VisiteurCreate(VisiteurBase):
    """Schéma pour la création d'un visiteur."""
    pass


class VisiteurUpdate(BaseModel):
    """Schéma pour la mise à jour d'un visiteur."""
    nom: Optional[str] = None
    prenom: Optional[str] = None
    telephone: Optional[str] = None
    email: Optional[EmailStr] = None
    organisation: Optional[str] = None
    fonction: Optional[str] = None
    objet_visite: Optional[str] = None
    personne_visitee: Optional[str] = None
    badge_numero: Optional[str] = None
    heure_sortie: Optional[datetime] = None


class VisiteurResponse(VisiteurBase):
    """Schéma pour la réponse visiteur."""
    id: int
    heure_entree: datetime
    heure_sortie: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class VisiteurList(BaseModel):
    """Schéma pour la liste des visiteurs."""
    visiteurs: list[VisiteurResponse]
    total: int
    page: int
    per_page: int
