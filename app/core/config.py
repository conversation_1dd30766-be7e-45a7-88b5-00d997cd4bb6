"""
Configuration de l'application.
"""

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Configuration de l'application."""
    
    PROJECT_NAME: str = "Gestion des Visiteurs"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # Configuration de la base de données
    DATABASE_URL: str = "sqlite:///./visiteurs.db"
    
    # Configuration JWT
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Configuration CORS
    BACKEND_CORS_ORIGINS: str = "http://localhost:3000,http://localhost:8000"

    # Environnement
    ENVIRONMENT: str = "development"

    class Config:
        env_file = ".env"


settings = Settings()
