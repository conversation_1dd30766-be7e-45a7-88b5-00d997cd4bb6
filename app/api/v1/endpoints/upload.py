"""
Endpoints pour l'upload de fichiers.
"""
import os
import uuid
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session

from app.database.database import get_db
from app.core.dependencies import get_current_active_user
from app.models.user import User
from app.core.config import settings

router = APIRouter()

# Dossier pour stocker les photos
UPLOAD_DIR = "static/uploads/photos"
os.makedirs(UPLOAD_DIR, exist_ok=True)

# Extensions autorisées
ALLOWED_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif", ".webp"}
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB


def validate_image_file(file: UploadFile) -> bool:
    """Valide le fichier image."""
    if not file.filename:
        return False
    
    # Vérifier l'extension
    file_ext = os.path.splitext(file.filename)[1].lower()
    if file_ext not in ALLOWED_EXTENSIONS:
        return False
    
    return True


@router.post("/photo", response_model=dict)
async def upload_photo(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """
    Upload une photo de visiteur.
    """
    try:
        # Valider le fichier
        if not validate_image_file(file):
            return {
                "success": False,
                "message": "Format de fichier non supporté. Utilisez JPG, PNG, GIF ou WebP.",
                "data": None
            }
        
        # Vérifier la taille du fichier
        file_content = await file.read()
        if len(file_content) > MAX_FILE_SIZE:
            return {
                "success": False,
                "message": "Le fichier est trop volumineux. Taille maximale: 5MB.",
                "data": None
            }
        
        # Générer un nom de fichier unique
        file_ext = os.path.splitext(file.filename)[1].lower()
        unique_filename = f"{uuid.uuid4()}{file_ext}"
        file_path = os.path.join(UPLOAD_DIR, unique_filename)
        
        # Sauvegarder le fichier
        with open(file_path, "wb") as f:
            f.write(file_content)
        
        # Retourner le chemin relatif pour l'accès web
        web_path = f"/static/uploads/photos/{unique_filename}"
        
        return {
            "success": True,
            "message": "Photo uploadée avec succès",
            "data": {
                "photo_path": web_path,
                "filename": unique_filename
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"Erreur lors de l'upload: {str(e)}",
            "data": None
        }


@router.delete("/photo/{filename}", response_model=dict)
async def delete_photo(
    filename: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Supprime une photo.
    """
    try:
        file_path = os.path.join(UPLOAD_DIR, filename)
        
        if os.path.exists(file_path):
            os.remove(file_path)
            return {
                "success": True,
                "message": "Photo supprimée avec succès",
                "data": None
            }
        else:
            return {
                "success": False,
                "message": "Photo non trouvée",
                "data": None
            }
            
    except Exception as e:
        return {
            "success": False,
            "message": f"Erreur lors de la suppression: {str(e)}",
            "data": None
        }
