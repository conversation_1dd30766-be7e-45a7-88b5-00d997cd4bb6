{% extends "base.html" %}

{% block title %}Liste des Visiteurs - Gestion des Visiteurs{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="h-12 w-12 bg-primary-500 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">LISTE DES VISITEURS</h1>
                        <p class="text-gray-600">Gestion et suivi des visiteurs</p>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <a href="/dashboard" class="btn-primary">
                        <svg class="h-5 w-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Nouveau visiteur
                    </a>
                    <button id="logoutButton" class="text-gray-500 hover:text-gray-700">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Filtres et recherche -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div class="flex-1 max-w-lg">
                    <div class="relative">
                        <input 
                            type="text" 
                            id="searchInput" 
                            placeholder="Rechercher par nom, organisation, personne visitée..."
                            class="input-field pl-10"
                        >
                        <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <button id="showPresentBtn" class="px-4 py-2 bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors">
                        Visiteurs présents
                    </button>
                    <button id="showAllBtn" class="px-4 py-2 bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 transition-colors">
                        Tous les visiteurs
                    </button>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
            <span id="errorText"></span>
        </div>
        <div id="successMessage" class="hidden bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
            <span id="successText"></span>
        </div>

        <!-- Statistiques -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-full">
                        <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total visiteurs</p>
                        <p class="text-2xl font-semibold text-gray-900" id="totalVisiteurs">-</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-full">
                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Présents</p>
                        <p class="text-2xl font-semibold text-gray-900" id="visiteursPresents">-</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-gray-100 rounded-full">
                        <svg class="h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Sortis</p>
                        <p class="text-2xl font-semibold text-gray-900" id="visiteursSortis">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table des visiteurs -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Liste des visiteurs</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Photo</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visiteur</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organisation</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visite</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="visiteursTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- Les données seront chargées ici par JavaScript -->
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button id="prevPageMobile" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Précédent
                    </button>
                    <button id="nextPageMobile" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Suivant
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Affichage de <span id="startItem">1</span> à <span id="endItem">10</span> sur <span id="totalItems">0</span> résultats
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="pagination">
                            <!-- Les boutons de pagination seront générés ici -->
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Vérifier l'authentification
    const token = localStorage.getItem('access_token');
    if (!token) {
        window.location.href = '/login';
    }

    // Variables globales
    let currentPage = 1;
    let currentSearch = '';
    let showOnlyPresent = false;
    const itemsPerPage = 10;

    // Charger les visiteurs
    async function loadVisiteurs(page = 1, search = '', onlyPresent = false) {
        try {
            let url = `/api/v1/visiteurs/?skip=${(page - 1) * itemsPerPage}&limit=${itemsPerPage}`;

            if (search) {
                url += `&search=${encodeURIComponent(search)}`;
            }

            if (onlyPresent) {
                url = '/api/v1/visiteurs/presents';
            }

            const response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();

            if (result.success) {
                if (onlyPresent) {
                    displayVisiteurs(result.data, result.data.length, page);
                    updateStats(result.data.length, result.data.length, 0);
                } else {
                    displayVisiteurs(result.data.visiteurs, result.data.total, page);
                    await loadStats();
                }
            } else {
                showError(result.message);
            }
        } catch (error) {
            showError('Erreur lors du chargement des visiteurs');
        }
    }

    // Charger les statistiques
    async function loadStats() {
        try {
            const [allResponse, presentResponse] = await Promise.all([
                fetch('/api/v1/visiteurs/?skip=0&limit=1000', {
                    headers: { 'Authorization': `Bearer ${token}` }
                }),
                fetch('/api/v1/visiteurs/presents', {
                    headers: { 'Authorization': `Bearer ${token}` }
                })
            ]);

            const allResult = await allResponse.json();
            const presentResult = await presentResponse.json();

            if (allResult.success && presentResult.success) {
                const total = allResult.data.total;
                const present = presentResult.data.length;
                const sortis = total - present;

                updateStats(total, present, sortis);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des statistiques:', error);
        }
    }

    // Mettre à jour les statistiques
    function updateStats(total, present, sortis) {
        document.getElementById('totalVisiteurs').textContent = total;
        document.getElementById('visiteursPresents').textContent = present;
        document.getElementById('visiteursSortis').textContent = sortis;
    }

    // Afficher les visiteurs
    function displayVisiteurs(visiteurs, total, page) {
        const tbody = document.getElementById('visiteursTableBody');
        tbody.innerHTML = '';

        if (visiteurs.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                        Aucun visiteur trouvé
                    </td>
                </tr>
            `;
            return;
        }

        visiteurs.forEach(visiteur => {
            const row = createVisiteurRow(visiteur);
            tbody.appendChild(row);
        });

        updatePagination(total, page);
    }

    // Créer une ligne de visiteur
    function createVisiteurRow(visiteur) {
        const tr = document.createElement('tr');
        tr.className = 'hover:bg-gray-50';

        const isPresent = !visiteur.heure_sortie;
        const statusClass = isPresent ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
        const statusText = isPresent ? 'Présent' : 'Sorti';

        tr.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                ${visiteur.photo_path ?
                    `<img src="${visiteur.photo_path}" alt="Photo" class="h-12 w-12 rounded-full object-cover">` :
                    `<div class="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                        <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>`
                }
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">${visiteur.nom} ${visiteur.prenom} ${visiteur.postnom}</div>
                <div class="text-sm text-gray-500">${visiteur.sexe === 'M' ? 'Masculin' : 'Féminin'} • ${visiteur.nationalite}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">${visiteur.telephone1}</div>
                ${visiteur.email ? `<div class="text-sm text-gray-500">${visiteur.email}</div>` : ''}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">${visiteur.organisation || '-'}</div>
                <div class="text-sm text-gray-500">${visiteur.fonction}</div>
            </td>
            <td class="px-6 py-4">
                <div class="text-sm text-gray-900">${visiteur.personne_visitee}</div>
                <div class="text-sm text-gray-500">${visiteur.objet_visite.substring(0, 50)}${visiteur.objet_visite.length > 50 ? '...' : ''}</div>
                <div class="text-xs text-gray-400">${formatDate(visiteur.heure_entree)}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClass}">
                    ${statusText}
                </span>
                ${visiteur.badge_numero ? `<div class="text-xs text-gray-500 mt-1">Badge: ${visiteur.badge_numero}</div>` : ''}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                    ${isPresent ?
                        `<button onclick="marquerSortie(${visiteur.id})" class="text-red-600 hover:text-red-900">Marquer sortie</button>` :
                        `<span class="text-gray-400">Sorti le ${formatDate(visiteur.heure_sortie)}</span>`
                    }
                </div>
            </td>
        `;

        return tr;
    }

    // Marquer la sortie d'un visiteur
    async function marquerSortie(visiteurId) {
        if (!confirm('Êtes-vous sûr de vouloir marquer la sortie de ce visiteur ?')) {
            return;
        }

        try {
            const response = await fetch(`/api/v1/visiteurs/${visiteurId}/sortie`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();

            if (result.success) {
                showSuccess('Sortie enregistrée avec succès');
                loadVisiteurs(currentPage, currentSearch, showOnlyPresent);
            } else {
                showError(result.message);
            }
        } catch (error) {
            showError('Erreur lors de l\'enregistrement de la sortie');
        }
    }

    // Formater une date
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // Mettre à jour la pagination
    function updatePagination(total, currentPage) {
        const totalPages = Math.ceil(total / itemsPerPage);
        const startItem = (currentPage - 1) * itemsPerPage + 1;
        const endItem = Math.min(currentPage * itemsPerPage, total);

        document.getElementById('startItem').textContent = startItem;
        document.getElementById('endItem').textContent = endItem;
        document.getElementById('totalItems').textContent = total;

        // Générer les boutons de pagination
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';

        // Bouton précédent
        if (currentPage > 1) {
            pagination.appendChild(createPageButton(currentPage - 1, 'Précédent'));
        }

        // Boutons de pages
        for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
            pagination.appendChild(createPageButton(i, i.toString(), i === currentPage));
        }

        // Bouton suivant
        if (currentPage < totalPages) {
            pagination.appendChild(createPageButton(currentPage + 1, 'Suivant'));
        }
    }

    // Créer un bouton de pagination
    function createPageButton(page, text, isActive = false) {
        const button = document.createElement('button');
        button.className = `relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
            isActive
                ? 'z-10 bg-primary-50 border-primary-500 text-primary-600'
                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
        }`;
        button.textContent = text;
        button.onclick = () => {
            currentPage = page;
            loadVisiteurs(page, currentSearch, showOnlyPresent);
        };
        return button;
    }

    // Afficher un message d'erreur
    function showError(message) {
        document.getElementById('errorText').textContent = message;
        document.getElementById('errorMessage').classList.remove('hidden');
        document.getElementById('successMessage').classList.add('hidden');
    }

    // Afficher un message de succès
    function showSuccess(message) {
        document.getElementById('successText').textContent = message;
        document.getElementById('successMessage').classList.remove('hidden');
        document.getElementById('errorMessage').classList.add('hidden');
    }

    // Event listeners
    document.getElementById('searchInput').addEventListener('input', function(e) {
        currentSearch = e.target.value;
        currentPage = 1;
        loadVisiteurs(currentPage, currentSearch, showOnlyPresent);
    });

    document.getElementById('showPresentBtn').addEventListener('click', function() {
        showOnlyPresent = true;
        currentPage = 1;
        loadVisiteurs(currentPage, currentSearch, showOnlyPresent);
        this.classList.add('bg-green-200');
        document.getElementById('showAllBtn').classList.remove('bg-gray-200');
    });

    document.getElementById('showAllBtn').addEventListener('click', function() {
        showOnlyPresent = false;
        currentPage = 1;
        loadVisiteurs(currentPage, currentSearch, showOnlyPresent);
        this.classList.add('bg-gray-200');
        document.getElementById('showPresentBtn').classList.remove('bg-green-200');
    });

    document.getElementById('logoutButton').addEventListener('click', function() {
        if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
            localStorage.removeItem('access_token');
            localStorage.removeItem('user');
            window.location.href = '/login';
        }
    });

    // Charger les données au démarrage
    loadVisiteurs();
</script>
{% endblock %}
