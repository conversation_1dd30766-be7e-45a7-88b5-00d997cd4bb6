"""
Modèle SQLAlchemy pour les visiteurs.
"""
from sqlalchemy import Column, Integer, String, DateTime, Text
from sqlalchemy.sql import func

from app.database.database import Base


class Visiteur(Base):
    """Modèle pour les visiteurs."""
    
    __tablename__ = "visiteurs"

    id = Column(Integer, primary_key=True, index=True)
    
    # Informations personnelles
    nom = Column(String(100), nullable=False, index=True)
    postnom = Column(String(100), nullable=False, index=True)
    prenom = Column(String(100), nullable=False, index=True)
    sexe = Column(String(2), nullable=False)
    nationalite = Column(String(200), nullable=False)
    telephone1 = Column(String(20), nullable=False)
    telephone2 = Column(String(20), nullable=False)
    telephone3 = Column(String(20), nullable=True)
    telephone4 = Column(String(20), nullable=True)
    email = Column(String(100), nullable=True)
    
    # Informations professionnelles
    organisation = Column(String(500), nullable=True)
    fonction = Column(String(500), nullable=False)
    
    # Informations de visite
    objet_visite = Column(Text, nullable=False)
    personne_visitee = Column(String(200), nullable=False)
    badge_numero = Column(String(20), nullable=True, unique=True)
    
    # Horodatage
    heure_entree = Column(DateTime(timezone=True), server_default=func.now())
    heure_sortie = Column(DateTime(timezone=True), nullable=True)
    
    # Métadonnées
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<Visiteur(nom='{self.nom}', prenom='{self.prenom}', organisation='{self.organisation}')>"
