"""
Modèle SQLAlchemy pour les utilisateurs.
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>n, Integer, String, DateTime
from sqlalchemy.sql import func

from app.database.database import Base


class User(Base):
    """Modèle pour les utilisateurs du système."""
    
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=True)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    nationalite = Column(String(200), nullable=False)

    def __repr__(self):
        return f"<User(username='{self.username}', email='{self.email}')>"
