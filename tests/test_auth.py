"""
Tests pour l'authentification.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.database.database import get_db, Base
from app.models.user import User

# Base de données de test en mémoire
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture
def client():
    Base.metadata.create_all(bind=engine)
    with TestClient(app) as c:
        yield c
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def test_user(client):
    """Crée un utilisateur de test."""
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "nationalite": "Française"
    }
    response = client.post("/api/v1/auth/register", json=user_data)
    return response.json()

def test_register_user(client):
    """Test de création d'utilisateur."""
    user_data = {
        "username": "newuser",
        "email": "<EMAIL>",
        "full_name": "New User",
        "nationalite": "Congolaise"
    }
    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["username"] == "newuser"

def test_login_user(client, test_user):
    """Test de connexion utilisateur."""
    login_data = {
        "username": "testuser",
        "remember_me": False
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "access_token" in data["data"]

def test_login_invalid_user(client):
    """Test de connexion avec utilisateur inexistant."""
    login_data = {
        "username": "nonexistent",
        "remember_me": False
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is False

def test_get_current_user(client, test_user):
    """Test de récupération de l'utilisateur connecté."""
    # Se connecter d'abord
    login_data = {
        "username": "testuser",
        "remember_me": False
    }
    login_response = client.post("/api/v1/auth/login", json=login_data)
    token = login_response.json()["data"]["access_token"]
    
    # Récupérer les infos utilisateur
    headers = {"Authorization": f"Bearer {token}"}
    response = client.get("/api/v1/auth/me", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == "testuser"

def test_protected_route_without_token(client):
    """Test d'accès à une route protégée sans token."""
    response = client.get("/api/v1/auth/me")
    assert response.status_code == 403
