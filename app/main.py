"""
Application principale FastAPI pour la gestion des visiteurs.
"""
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

from app.core.config import settings
from app.api.v1.api import api_router
from app.api.v1.endpoints import pages

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="Application de gestion des visiteurs"
)

# Configuration des fichiers statiques
app.mount("/static", StaticFiles(directory="static"), name="static")

# Configuration des templates
templates = Jinja2Templates(directory="templates")

# Inclusion des routes API
app.include_router(api_router, prefix=settings.API_V1_STR)

# Inclusion des routes des pages
app.include_router(pages.router)

@app.get("/")
async def root():
    """Page d'accueil - redirection vers la page de connexion."""
    return {"message": "Application de gestion des visiteurs"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

