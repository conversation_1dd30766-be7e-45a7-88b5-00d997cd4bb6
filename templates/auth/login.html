{% extends "base.html" %}

{% block title %}Connexion - Gestion des Visiteurs{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Logo et titre -->
        <div class="text-center">
            <div class="mx-auto h-20 w-20 bg-primary-500 rounded-full flex items-center justify-center mb-6">
                <svg class="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-2">Sign In</h2>
            <p class="text-gray-600">Accédez à votre espace de gestion</p>
        </div>

        <!-- Formulaire de connexion -->
        <div class="bg-white rounded-xl shadow-lg p-8">
            <form id="loginForm" class="space-y-6">
                <!-- Message d'erreur -->
                <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <span id="errorText"></span>
                </div>

                <!-- Message de succès -->
                <div id="successMessage" class="hidden bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <span id="successText"></span>
                </div>

                <!-- Champ Username -->
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                        Username
                    </label>
                    <input 
                        id="username" 
                        name="username" 
                        type="text" 
                        required 
                        class="input-field"
                        placeholder="Entrez votre nom d'utilisateur"
                    >
                </div>

                <!-- Message d'information -->
                <div class="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg">
                    <p class="text-sm">
                        <svg class="inline h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Saisissez simplement votre nom d'utilisateur pour accéder à l'application.
                    </p>
                </div>

                <!-- Options -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input 
                            id="remember_me" 
                            name="remember_me" 
                            type="checkbox" 
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        >
                        <label for="remember_me" class="ml-2 block text-sm text-gray-700">
                            Remember Me
                        </label>
                    </div>
                    <div class="text-sm">
                        <span class="text-gray-500">
                            Accès simplifié sans mot de passe
                        </span>
                    </div>
                </div>

                <!-- Bouton de connexion -->
                <div>
                    <button 
                        type="submit" 
                        id="loginButton"
                        class="w-full btn-primary py-3 text-lg font-semibold"
                    >
                        <span id="loginButtonText">Login</span>
                        <svg id="loginSpinner" class="hidden animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>

        <!-- Lien d'inscription (optionnel) -->
        <div class="text-center">
            <p class="text-sm text-gray-600">
                Pas encore de compte ? 
                <a href="#" class="font-medium text-primary-600 hover:text-primary-500">
                    Contactez l'administrateur
                </a>
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Gestion du formulaire de connexion
    document.getElementById('loginForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const loginButton = document.getElementById('loginButton');
        const loginButtonText = document.getElementById('loginButtonText');
        const loginSpinner = document.getElementById('loginSpinner');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');
        
        // Désactiver le bouton et afficher le spinner
        loginButton.disabled = true;
        loginButtonText.textContent = 'Connexion...';
        loginSpinner.classList.remove('hidden');
        
        // Masquer les messages précédents
        errorMessage.classList.add('hidden');
        successMessage.classList.add('hidden');
        
        try {
            const formData = new FormData(this);
            const data = {
                username: formData.get('username'),
                remember_me: formData.get('remember_me') === 'on'
            };
            
            const response = await fetch('/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Stocker le token
                localStorage.setItem('access_token', result.data.access_token);
                localStorage.setItem('user', JSON.stringify(result.data.user));
                
                // Afficher le message de succès
                document.getElementById('successText').textContent = result.message;
                successMessage.classList.remove('hidden');
                
                // Rediriger vers le dashboard
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1000);
            } else {
                // Afficher l'erreur
                document.getElementById('errorText').textContent = result.message;
                errorMessage.classList.remove('hidden');
            }
        } catch (error) {
            // Afficher l'erreur
            document.getElementById('errorText').textContent = 'Erreur de connexion. Veuillez réessayer.';
            errorMessage.classList.remove('hidden');
        } finally {
            // Réactiver le bouton
            loginButton.disabled = false;
            loginButtonText.textContent = 'Login';
            loginSpinner.classList.add('hidden');
        }
    });
</script>
{% endblock %}
